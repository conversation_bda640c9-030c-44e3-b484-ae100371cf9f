# CMS NH88 - Database Schema Documentation

## 📋 Overview

Hệ thống CMS NH88 sử dụng MySQL 8.0+ với 45+ tables được tổ chức thành 7 nhóm chính:

1. **User Management** (6 tables)
2. **Department Structure** (4 tables)  
3. **Backend Management** (2 tables)
4. **Revenue Tracking** (8 tables)
5. **Expense Management** (7 tables)
6. **Performance Analytics** (8 tables)
7. **Data Management** (9 tables)
8. **System & Analytics** (5 tables)

## 🔐 User Management

### users
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    employee_code VARCHAR(50) UNIQUE,
    hire_date DATE,
    birth_date DATE,
    gender ENUM('male', 'female', 'other'),
    address TEXT,
    emergency_contact VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    password_changed_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_employee_code (employee_code),
    INDEX idx_email (email),
    INDEX idx_active (is_active)
);
```

### roles
```sql
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(100) NOT NULL UNIQUE,
    role_code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    level INT NOT NULL, -- 1=Manager, 2=Team Leader, 3=Team Member
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_level (level),
    INDEX idx_code (role_code)
);
```

### permissions
```sql
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    permission_code VARCHAR(50) NOT NULL UNIQUE,
    module VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL, -- create, read, update, delete, approve
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_module (module),
    INDEX idx_code (permission_code)
);
```

### user_roles
```sql
CREATE TABLE user_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    role_id INT NOT NULL,
    department_id INT NOT NULL,
    assigned_by BIGINT NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    INDEX idx_user_dept (user_id, department_id),
    INDEX idx_role_dept (role_id, department_id)
);
```

### role_permissions
```sql
CREATE TABLE role_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (permission_id) REFERENCES permissions(id),
    UNIQUE KEY unique_role_permission (role_id, permission_id)
);
```

### user_sessions
```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_time DATETIME NOT NULL,
    last_activity DATETIME NOT NULL,
    logout_time DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user (user_id),
    INDEX idx_token (session_token),
    INDEX idx_active (is_active)
);
```

## 🏢 Department Structure

### departments
```sql
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    code VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    manager_id BIGINT,
    parent_department_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_manager (manager_id),
    INDEX idx_parent (parent_department_id)
);
```

### department_hierarchy
```sql
CREATE TABLE department_hierarchy (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL,
    parent_department_id INT,
    manager_id BIGINT,
    level INT NOT NULL DEFAULT 1,
    path VARCHAR(500), -- Hierarchical path like "/1/2/3"
    is_active BOOLEAN DEFAULT TRUE,
    effective_from DATE NOT NULL,
    effective_to DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (parent_department_id) REFERENCES departments(id),
    FOREIGN KEY (manager_id) REFERENCES users(id),
    INDEX idx_department (department_id),
    INDEX idx_parent (parent_department_id),
    INDEX idx_manager (manager_id),
    INDEX idx_path (path)
);
```

### team_structures
```sql
CREATE TABLE team_structures (
    id INT PRIMARY KEY AUTO_INCREMENT,
    team_name VARCHAR(100) NOT NULL,
    department_id INT NOT NULL,
    team_leader_id BIGINT NOT NULL,
    parent_team_id INT,
    team_type ENUM('sales', 'marketing', 'support', 'technical', 'other') NOT NULL,
    max_members INT DEFAULT 10,
    current_members INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (team_leader_id) REFERENCES users(id),
    FOREIGN KEY (parent_team_id) REFERENCES team_structures(id),
    INDEX idx_department (department_id),
    INDEX idx_leader (team_leader_id),
    INDEX idx_parent_team (parent_team_id)
);
```

### team_members
```sql
CREATE TABLE team_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    team_id INT NOT NULL,
    user_id BIGINT NOT NULL,
    position VARCHAR(100),
    joined_date DATE NOT NULL,
    left_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    added_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (team_id) REFERENCES team_structures(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (added_by) REFERENCES users(id),
    UNIQUE KEY unique_team_user_active (team_id, user_id, is_active),
    INDEX idx_team (team_id),
    INDEX idx_user (user_id)
);
```

## 🖥️ Backend Management

### backends
```sql
CREATE TABLE backends (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backend_name VARCHAR(100) NOT NULL,
    backend_code VARCHAR(50) NOT NULL UNIQUE,
    backend_url VARCHAR(255),
    api_endpoint VARCHAR(255),
    database_config JSON,
    is_active BOOLEAN DEFAULT TRUE,
    max_capacity INT DEFAULT 1000,
    current_load INT DEFAULT 0,
    maintenance_mode BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code (backend_code),
    INDEX idx_active (is_active)
);
```

### user_backend_assignments
```sql
CREATE TABLE user_backend_assignments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    backend_id INT NOT NULL,
    department_id INT NOT NULL,
    assignment_type ENUM('primary', 'secondary', 'backup') DEFAULT 'primary',
    access_level ENUM('read', 'write', 'admin') DEFAULT 'read',
    assigned_by BIGINT NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (backend_id) REFERENCES backends(id),
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    INDEX idx_user_backend (user_id, backend_id),
    INDEX idx_department_backend (department_id, backend_id)
);
```

## 💰 Revenue Tracking

### revenue_streams
```sql
CREATE TABLE revenue_streams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL,
    stream_name VARCHAR(100) NOT NULL,
    stream_type ENUM('direct_sales', 'commission', 'recurring', 'one_time') NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (department_id) REFERENCES departments(id),
    INDEX idx_department (department_id)
);
```

### revenue_records
```sql
CREATE TABLE revenue_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL,
    revenue_stream_id INT NOT NULL,
    employee_id BIGINT NOT NULL,
    customer_id BIGINT,
    backend_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    commission_amount DECIMAL(15,2) DEFAULT 0,
    revenue_date DATETIME NOT NULL,
    transaction_type ENUM('deposit', 'bet', 'commission', 'bonus', 'other') NOT NULL,
    source_reference VARCHAR(255), -- Campaign ID, Lead ID, etc.
    notes TEXT,
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (revenue_stream_id) REFERENCES revenue_streams(id),
    INDEX idx_dept_date (department_id, revenue_date),
    INDEX idx_employee_date (employee_id, revenue_date),
    INDEX idx_backend_date (backend_id, revenue_date),
    INDEX idx_customer (customer_id)
);
```

### ftd_records
```sql
CREATE TABLE ftd_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    department_id INT NOT NULL,
    backend_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    deposit_date DATETIME NOT NULL,
    source VARCHAR(50), -- SEO, ADS, Sale, etc.
    campaign_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_department_date (department_id, deposit_date),
    INDEX idx_backend_date (backend_id, deposit_date),
    INDEX idx_source_date (source, deposit_date)
);
```

### betting_records
```sql
CREATE TABLE betting_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    department_id INT NOT NULL,
    backend_id INT NOT NULL,
    game_type VARCHAR(50) NOT NULL,
    bet_amount DECIMAL(15,2) NOT NULL,
    win_amount DECIMAL(15,2) DEFAULT 0,
    bet_date DATETIME NOT NULL,
    status ENUM('pending', 'win', 'lose', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_dept_game_date (department_id, game_type, bet_date),
    INDEX idx_backend_date (backend_id, bet_date)
);
```

### commission_rules
```sql
CREATE TABLE commission_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL,
    rule_name VARCHAR(100) NOT NULL,
    rule_type ENUM('percentage', 'fixed', 'tiered', 'performance_based') NOT NULL,
    base_percentage DECIMAL(5,2),
    fixed_amount DECIMAL(15,2),
    min_threshold DECIMAL(15,2),
    max_threshold DECIMAL(15,2),
    performance_multiplier DECIMAL(3,2) DEFAULT 1.00,
    effective_from DATE NOT NULL,
    effective_to DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (department_id) REFERENCES departments(id),
    INDEX idx_department_active (department_id, is_active)
);
```

### commission_tiers
```sql
CREATE TABLE commission_tiers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    commission_rule_id INT NOT NULL,
    tier_level INT NOT NULL,
    min_amount DECIMAL(15,2) NOT NULL,
    max_amount DECIMAL(15,2),
    commission_rate DECIMAL(5,2) NOT NULL,
    
    FOREIGN KEY (commission_rule_id) REFERENCES commission_rules(id),
    INDEX idx_rule_tier (commission_rule_id, tier_level)
);
```

### monthly_targets
```sql
CREATE TABLE monthly_targets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL,
    employee_id BIGINT,
    target_month DATE NOT NULL, -- YYYY-MM-01
    revenue_target DECIMAL(15,2) NOT NULL,
    ftd_target INT DEFAULT 0,
    customer_target INT DEFAULT 0,
    actual_revenue DECIMAL(15,2) DEFAULT 0,
    actual_ftd INT DEFAULT 0,
    actual_customers INT DEFAULT 0,
    achievement_percentage DECIMAL(5,2) DEFAULT 0,
    bonus_earned DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (department_id) REFERENCES departments(id),
    UNIQUE KEY unique_dept_emp_month (department_id, employee_id, target_month),
    INDEX idx_department_month (department_id, target_month)
);
```

## 💸 Expense Management

### expense_categories
```sql
CREATE TABLE expense_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL UNIQUE,
    parent_category_id INT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    requires_approval BOOLEAN DEFAULT TRUE,
    approval_limit DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_parent (parent_category_id),
    INDEX idx_code (code)
);
```

### expense_records
```sql
CREATE TABLE expense_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL,
    category_id INT NOT NULL,
    employee_id BIGINT NOT NULL,
    expense_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'VND',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    amount_vnd DECIMAL(15,2) NOT NULL,
    vendor_name VARCHAR(255),
    description TEXT NOT NULL,
    receipt_url VARCHAR(500),
    google_drive_file_id VARCHAR(255),
    reference_number VARCHAR(100),
    payment_method ENUM('cash', 'bank_transfer', 'credit_card', 'other') NOT NULL,
    status ENUM('draft', 'submitted', 'approved', 'rejected', 'paid') DEFAULT 'draft',
    submitted_at DATETIME,
    approved_at DATETIME,
    approved_by BIGINT,
    rejected_reason TEXT,
    paid_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (category_id) REFERENCES expense_categories(id),
    INDEX idx_dept_date (department_id, expense_date),
    INDEX idx_employee_date (employee_id, expense_date),
    INDEX idx_status (status),
    INDEX idx_category_date (category_id, expense_date)
);
```

### budget_allocations
```sql
CREATE TABLE budget_allocations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL,
    category_id INT NOT NULL,
    budget_period ENUM('monthly', 'quarterly', 'yearly') NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    allocated_amount DECIMAL(15,2) NOT NULL,
    spent_amount DECIMAL(15,2) DEFAULT 0,
    remaining_amount DECIMAL(15,2) DEFAULT 0,
    utilization_percentage DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (category_id) REFERENCES expense_categories(id),
    UNIQUE KEY unique_dept_cat_period (department_id, category_id, period_start),
    INDEX idx_department_period (department_id, period_start, period_end)
);
```

### approval_workflows
```sql
CREATE TABLE approval_workflows (
    id INT PRIMARY KEY AUTO_INCREMENT,
    expense_id BIGINT NOT NULL,
    approver_id BIGINT NOT NULL,
    approval_level INT NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'skipped') DEFAULT 'pending',
    comments TEXT,
    action_date DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (expense_id) REFERENCES expense_records(id),
    INDEX idx_expense (expense_id),
    INDEX idx_approver_status (approver_id, status)
);
```

### google_drive_files
```sql
CREATE TABLE google_drive_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    expense_id BIGINT NOT NULL,
    file_id VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(50),
    file_size BIGINT,
    drive_folder_id VARCHAR(255),
    download_url VARCHAR(500),
    view_url VARCHAR(500),
    upload_date DATETIME NOT NULL,
    uploaded_by BIGINT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (expense_id) REFERENCES expense_records(id),
    UNIQUE KEY unique_expense_file (expense_id, file_id),
    INDEX idx_file_id (file_id),
    INDEX idx_folder (drive_folder_id)
);
```

## 📊 Performance Analytics

### performance_metrics
```sql
CREATE TABLE performance_metrics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(100) NOT NULL,
    metric_code VARCHAR(50) NOT NULL UNIQUE,
    department_id INT NOT NULL,
    metric_type ENUM('count', 'percentage', 'currency', 'ratio', 'score') NOT NULL,
    calculation_method TEXT,
    target_value DECIMAL(15,4),
    unit VARCHAR(20),
    is_higher_better BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (department_id) REFERENCES departments(id),
    INDEX idx_department (department_id),
    INDEX idx_code (metric_code)
);
```

### seo_performance
```sql
CREATE TABLE seo_performance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    date DATE NOT NULL,
    keyword VARCHAR(255) NOT NULL,
    url VARCHAR(500),
    search_engine ENUM('google', 'bing', 'yahoo') DEFAULT 'google',
    position INT,
    search_volume INT,
    clicks INT DEFAULT 0,
    impressions INT DEFAULT 0,
    ctr DECIMAL(5,2) DEFAULT 0,
    conversions INT DEFAULT 0,
    revenue DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_date_keyword (date, keyword),
    INDEX idx_url_date (url, date),
    INDEX idx_position (position)
);
```

### ads_performance
```sql
CREATE TABLE ads_performance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    campaign_id VARCHAR(100) NOT NULL,
    campaign_name VARCHAR(255) NOT NULL,
    platform ENUM('facebook', 'google', 'tiktok', 'youtube', 'other') NOT NULL,
    ad_account_id VARCHAR(100),
    date DATE NOT NULL,
    impressions BIGINT DEFAULT 0,
    clicks BIGINT DEFAULT 0,
    spend DECIMAL(15,2) DEFAULT 0,
    conversions INT DEFAULT 0,
    revenue DECIMAL(15,2) DEFAULT 0,
    cpm DECIMAL(10,4) DEFAULT 0,
    cpc DECIMAL(10,4) DEFAULT 0,
    ctr DECIMAL(5,2) DEFAULT 0,
    roas DECIMAL(10,4) DEFAULT 0,
    quality_score DECIMAL(3,1),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_campaign_date (campaign_id, date),
    INDEX idx_platform_date (platform, date),
    INDEX idx_account_date (ad_account_id, date)
);
```

### sales_performance
```sql
CREATE TABLE sales_performance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    employee_id BIGINT NOT NULL,
    date DATE NOT NULL,
    calls_made INT DEFAULT 0,
    calls_answered INT DEFAULT 0,
    call_duration_minutes INT DEFAULT 0,
    leads_contacted INT DEFAULT 0,
    leads_qualified INT DEFAULT 0,
    appointments_set INT DEFAULT 0,
    deals_closed INT DEFAULT 0,
    revenue_generated DECIMAL(15,2) DEFAULT 0,
    customer_satisfaction_score DECIMAL(3,1),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_employee_date (employee_id, date),
    INDEX idx_date (date)
);
```

### email_performance
```sql
CREATE TABLE email_performance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    campaign_id VARCHAR(100) NOT NULL,
    campaign_name VARCHAR(255) NOT NULL,
    send_date DATETIME NOT NULL,
    list_size INT NOT NULL,
    emails_sent INT NOT NULL,
    emails_delivered INT DEFAULT 0,
    emails_opened INT DEFAULT 0,
    emails_clicked INT DEFAULT 0,
    unsubscribes INT DEFAULT 0,
    bounces INT DEFAULT 0,
    spam_complaints INT DEFAULT 0,
    conversions INT DEFAULT 0,
    revenue DECIMAL(15,2) DEFAULT 0,
    delivery_rate DECIMAL(5,2) DEFAULT 0,
    open_rate DECIMAL(5,2) DEFAULT 0,
    click_rate DECIMAL(5,2) DEFAULT 0,
    unsubscribe_rate DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_campaign_date (campaign_id, send_date),
    INDEX idx_send_date (send_date)
);
```

---

*Tiếp tục với Data Management và System tables...*
