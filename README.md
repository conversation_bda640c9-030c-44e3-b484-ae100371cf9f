# CMS NH88 - <PERSON><PERSON> Thống Quản Lý Do<PERSON>h Thu & Chi Phí

## 📋 Tổng Quan

Hệ thống CMS NH88 là một nền tảng quản lý toàn diện cho việc theo dõi doanh thu, chi phí và hiệu suất của các bộ phận kinh doanh. <PERSON>ệ thống hỗ trợ quản lý phân cấp, tracking FTD (First Time Deposit), phân tích hiệu suất quảng cáo và quản lý dữ liệu tự động.

## 🎯 Mục Tiêu

- **<PERSON> dõi doanh thu** theo từng bộ phận và nhân viên
- **Quản lý chi phí** với workflow phê duyệt
- **Phân tích hiệu suất** quảng cáo đa kênh
- **Quản lý dữ liệu** tự động với validation
- **<PERSON><PERSON> quyền** theo cấp bậc tổ chức
- **<PERSON><PERSON><PERSON> c<PERSON>o** real-time và analytics

## 🏗️ Kiến Trúc Hệ Thống

### Core Modules

```
├── Module 1: Dashboard & Analytics
├── Module 2: Revenue Management  
├── Module 3: Expense Management
├── Module 4: Performance Analytics
├── Module 5: Data Management
└── Module 6: HR & Department Management
```

### Technology Stack

**Backend:**
- Framework: Laravel 10+ / Node.js + Express
- Database: MySQL 8.0+
- Cache: Redis
- Queue: Redis/RabbitMQ
- Storage: AWS S3

**Frontend:**
- Framework: React.js + TypeScript
- Charts: Chart.js / D3.js
- UI: Ant Design / Material-UI
- State: Redux Toolkit

## 📊 Module Chi Tiết

### 1. Dashboard & Analytics
- **Biểu đồ FTD** theo thời gian và bộ phận
- **Biểu đồ Tổng cược** với breakdown
- **Tracking số người nạp tiền** và conversion
- **Báo cáo doanh thu** tổng hợp
- **KPI Dashboard** real-time

### 2. Revenue Management
- **Tracking doanh thu** theo 7 bộ phận:
  - SEO
  - Sale Khu A & B  
  - ADS
  - Spam
  - New_fran
  - Bộ phận mở rộng
- **Commission system** với rules phức tạp
- **Target management** hàng tháng
- **Performance bonuses** tự động

### 3. Expense Management
- **Chi phí theo bộ phận** với categorization
- **Google Drive integration** cho receipts
- **Approval workflow** đa cấp
- **Budget allocation** và monitoring
- **Expense analytics** và reporting

### 4. Performance Analytics
- **SEO Performance**: Keywords, rankings, traffic
- **ADS Performance**: ROAS, CPC, CTR, conversions
- **Sales Performance**: Calls, leads, conversions
- **Email Performance**: Open rates, CTR, revenue
- **A/B Testing** results tracking

### 5. Data Management
- **File Upload System** cho 3 loại data:
  - Đăng ký (Registration)
  - Nạp đầu (First Deposit)
  - Báo cáo thành viên (Member Reports)
- **Data Processing Pipeline** tự động
- **Validation & Error Handling**
- **Data Quality Metrics**

### 6. HR & Department Management
- **Hierarchical Structure**: 3 cấp bậc
  - Quản lý (Full access)
  - Tổ trưởng (Team management)
  - Tổ viên (Personal data only)
- **Backend Assignment** system
- **Role-based Access Control**
- **Audit Logging** toàn diện

## 🗄️ Database Schema

### Core Tables Overview
```sql
-- User Management (6 tables)
users, roles, permissions, user_roles, role_permissions, user_sessions

-- Department Structure (4 tables)
departments, department_hierarchy, team_structures, team_members

-- Backend Management (2 tables)
backends, user_backend_assignments

-- Revenue Tracking (8 tables)
revenue_streams, revenue_records, ftd_records, betting_records
commission_rules, commission_tiers, monthly_targets, department_targets

-- Expense Management (7 tables)
expense_categories, expense_records, budget_allocations
approval_workflows, google_drive_files, expense_templates, recurring_expenses

-- Performance Analytics (8 tables)
performance_metrics, seo_performance, ads_performance, sales_performance
email_performance, performance_targets, performance_alerts, ab_test_results

-- Data Management (9 tables)
file_uploads, data_processing_jobs, registration_data, first_deposit_data
member_report_data, data_validation_rules, data_transformation_rules
file_folders, file_access_permissions

-- System & Analytics (5 tables)
audit_logs, dashboard_cache, revenue_summary, performance_summary, data_quality_metrics
```

### Key Relationships
- **users** ↔ **departments** (many-to-many via user_roles)
- **departments** ↔ **backends** (many-to-many via user_backend_assignments)
- **revenue_records** ← **ftd_records, betting_records** (aggregation)
- **expense_records** ↔ **approval_workflows** (one-to-many)
- **file_uploads** ↔ **data_processing_jobs** (one-to-many)

### Database Indexes Strategy
```sql
-- Performance critical indexes
CREATE INDEX idx_revenue_dept_date ON revenue_records(department_id, revenue_date);
CREATE INDEX idx_ftd_dept_date ON ftd_records(department_id, deposit_date);
CREATE INDEX idx_expense_status ON expense_records(status, expense_date);
CREATE INDEX idx_performance_date ON ads_performance(date, platform);
CREATE INDEX idx_user_active ON users(is_active, department_id);
```

## 🚀 Development Roadmap

### Phase 1: Foundation (4-6 weeks)
- [ ] Database setup với full schema
- [ ] Authentication & authorization system
- [ ] Basic dashboard với core charts
- [ ] User management interface

### Phase 2: Core Business Logic (6-8 weeks)
- [ ] Revenue tracking implementation
- [ ] Data upload & processing system
- [ ] Department management features
- [ ] Basic reporting functionality

### Phase 3: Advanced Features (4-6 weeks)
- [ ] Performance analytics dashboard
- [ ] Expense management với Google Drive
- [ ] Advanced reporting & export
- [ ] System optimization & security

## 📋 Features Checklist

### Dashboard
- [ ] FTD analytics charts
- [ ] Revenue trend analysis
- [ ] Department performance comparison
- [ ] Real-time KPI monitoring
- [ ] Custom date range filtering

### Revenue Management
- [ ] Multi-department revenue tracking
- [ ] Commission calculation engine
- [ ] Target vs actual comparison
- [ ] Performance bonus calculation
- [ ] Customer attribution modeling

### Expense Management
- [ ] Expense submission interface
- [ ] Multi-level approval workflow
- [ ] Google Drive integration
- [ ] Budget monitoring & alerts
- [ ] Expense analytics & reporting

### Performance Analytics
- [ ] SEO keyword tracking
- [ ] ADS campaign performance
- [ ] Sales team metrics
- [ ] Email marketing analytics
- [ ] Cross-channel attribution

### Data Management
- [ ] Drag & drop file upload
- [ ] Automated data processing
- [ ] Data validation & cleansing
- [ ] Error handling & reporting
- [ ] Data quality monitoring

### HR Management
- [ ] Hierarchical org structure
- [ ] Role-based permissions
- [ ] Backend assignment system
- [ ] Team management tools
- [ ] Audit trail & logging

## 🔧 Installation & Setup

### Prerequisites
```bash
- PHP 8.1+ / Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Composer / npm
```

### Backend Setup
```bash
# Clone repository
git clone https://github.com/your-org/cms-nh88.git
cd cms-nh88

# Install dependencies
composer install
npm install

# Environment setup
cp .env.example .env
php artisan key:generate

# Database setup
php artisan migrate
php artisan db:seed

# Start services
php artisan serve
php artisan queue:work
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

## 📊 API Documentation

### Authentication Endpoints
```http
POST   /api/auth/login              # User login
POST   /api/auth/logout             # User logout
POST   /api/auth/refresh            # Refresh JWT token
GET    /api/auth/me                 # Get current user info
POST   /api/auth/change-password    # Change password
```

### Dashboard Endpoints
```http
GET    /api/dashboard/analytics                    # Main analytics data
GET    /api/dashboard/ftd-summary/{period}         # FTD summary by period
GET    /api/dashboard/revenue-charts/{department}  # Revenue charts
GET    /api/dashboard/performance-kpis             # KPI metrics
GET    /api/dashboard/department-comparison        # Department comparison
```

### Revenue Management
```http
GET    /api/revenue/records                        # List revenue records
POST   /api/revenue/records                        # Create revenue record
PUT    /api/revenue/records/{id}                   # Update revenue record
DELETE /api/revenue/records/{id}                   # Delete revenue record
GET    /api/revenue/summary/{department}/{period}  # Revenue summary
GET    /api/revenue/commission/{employee}/{month}  # Commission calculation
GET    /api/revenue/targets/{department}           # Department targets
POST   /api/revenue/targets                        # Set new targets
```

### Expense Management
```http
GET    /api/expenses/records                       # List expense records
POST   /api/expenses/records                       # Submit expense
PUT    /api/expenses/records/{id}                  # Update expense
DELETE /api/expenses/records/{id}                  # Delete expense
POST   /api/expenses/{id}/approve                  # Approve expense
POST   /api/expenses/{id}/reject                   # Reject expense
GET    /api/expenses/budget/{department}           # Budget status
POST   /api/expenses/upload-receipt                # Upload receipt to GDrive
```

### Performance Analytics
```http
GET    /api/performance/seo/{date_range}           # SEO performance data
GET    /api/performance/ads/{platform}/{period}   # ADS performance
GET    /api/performance/sales/{employee}/{period} # Sales performance
GET    /api/performance/email/{campaign_id}       # Email campaign stats
GET    /api/performance/targets/{department}      # Performance targets
POST   /api/performance/alerts                    # Create performance alert
```

### Data Management
```http
POST   /api/data/upload                           # Upload data files
GET    /api/data/uploads/{id}/status              # Check upload status
GET    /api/data/processing-jobs                  # List processing jobs
POST   /api/data/validate/{upload_id}            # Validate uploaded data
GET    /api/data/quality-metrics/{upload_id}     # Data quality report
POST   /api/data/process/{upload_id}             # Process uploaded data
```

### HR & Department Management
```http
GET    /api/departments                           # List departments
POST   /api/departments                           # Create department
PUT    /api/departments/{id}                      # Update department
GET    /api/departments/{id}/hierarchy            # Department hierarchy
GET    /api/teams/{department_id}                 # List teams
POST   /api/teams                                 # Create team
GET    /api/users/{id}/permissions               # User permissions
POST   /api/users/{id}/assign-backend            # Assign backend
GET    /api/audit-logs                           # Audit trail
```

## 🔒 Security Features

- **JWT Authentication** với refresh tokens
- **Role-based Access Control** (RBAC)
- **Data encryption** at rest và in transit
- **API rate limiting** và throttling
- **Audit logging** cho tất cả actions
- **Input validation** và sanitization
- **CSRF protection** và XSS prevention

## 📈 Performance Optimization

- **Database indexing** cho queries thường dùng
- **Redis caching** cho dashboard data
- **Queue processing** cho heavy operations
- **Lazy loading** cho large datasets
- **CDN integration** cho static assets
- **Database query optimization**

## 🧪 Testing Strategy

### Backend Testing
```bash
# Unit tests
php artisan test --testsuite=Unit

# Feature tests
php artisan test --testsuite=Feature

# Coverage report
php artisan test --coverage --min=80

# Database testing
php artisan test --testsuite=Database
```

### Frontend Testing
```bash
# Unit tests
npm test

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e

# Coverage report
npm run test:coverage
```

### Test Categories
- **Unit Tests**: Individual functions và methods
- **Integration Tests**: API endpoints và database
- **Feature Tests**: Complete user workflows
- **Performance Tests**: Load testing và stress testing
- **Security Tests**: Authentication và authorization

## � Deployment Guide

### Production Environment
```bash
# Environment variables
APP_ENV=production
APP_DEBUG=false
DB_HOST=your-db-host
REDIS_HOST=your-redis-host
QUEUE_CONNECTION=redis

# Build và deploy
composer install --optimize-autoloader --no-dev
npm run build
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### Docker Deployment
```dockerfile
# Dockerfile example
FROM php:8.1-fpm
COPY . /var/www/html
RUN composer install --optimize-autoloader --no-dev
EXPOSE 9000
CMD ["php-fpm"]
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name cms-nh88.com;
    root /var/www/html/public;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        include fastcgi_params;
    }
}
```

## 📊 Monitoring & Logging

### Application Monitoring
- **Laravel Telescope** cho debugging
- **Sentry** cho error tracking
- **New Relic** cho performance monitoring
- **Prometheus + Grafana** cho metrics

### Log Management
```bash
# Log channels
LOG_CHANNEL=stack
LOG_STACK=single,daily,sentry

# Custom log levels
LOG_LEVEL=info
LOG_DEPRECATIONS_CHANNEL=null
```

## �📝 Contributing Guidelines

### Development Workflow
1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Follow coding standards (PSR-12 for PHP, ESLint for JS)
4. Write tests cho new features
5. Commit changes (`git commit -m 'Add amazing feature'`)
6. Push to branch (`git push origin feature/amazing-feature`)
7. Open Pull Request với detailed description

### Code Standards
- **PHP**: PSR-12 coding standard
- **JavaScript**: ESLint + Prettier
- **Database**: Snake_case naming
- **API**: RESTful conventions
- **Comments**: PHPDoc cho functions

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check database connection
php artisan tinker
>>> DB::connection()->getPdo();

# Clear config cache
php artisan config:clear
php artisan cache:clear
```

#### Queue Not Processing
```bash
# Check queue status
php artisan queue:work --verbose

# Restart queue workers
php artisan queue:restart

# Monitor failed jobs
php artisan queue:failed
```

#### Performance Issues
```bash
# Enable query logging
DB::enableQueryLog();

# Check slow queries
php artisan telescope:prune --hours=24

# Optimize database
php artisan optimize
```

### FAQ

**Q: Làm sao để thêm bộ phận mới?**
A: Vào Admin Panel → Departments → Create New Department, sau đó assign users và set permissions.

**Q: Tại sao dữ liệu upload không được xử lý?**
A: Kiểm tra queue workers đang chạy và file format đúng theo template.

**Q: Làm sao để backup dữ liệu?**
A: Sử dụng `php artisan backup:run` hoặc setup automated backup với cron jobs.

**Q: Commission không được tính đúng?**
A: Kiểm tra commission rules trong Admin Panel và verify calculation logic.

## 📊 Performance Benchmarks

### Expected Performance
- **Dashboard load time**: < 2 seconds
- **API response time**: < 500ms
- **File upload processing**: < 5 minutes for 10k records
- **Report generation**: < 30 seconds
- **Concurrent users**: 100+ simultaneous users

### Optimization Tips
- Enable Redis caching cho dashboard data
- Use database indexes cho frequent queries
- Implement pagination cho large datasets
- Use CDN cho static assets
- Enable gzip compression

## 📞 Support & Contact

### Documentation
- **Wiki**: [GitHub Wiki](https://github.com/your-org/cms-nh88/wiki)
- **API Docs**: [Postman Collection](https://documenter.getpostman.com/cms-nh88)
- **Video Tutorials**: [YouTube Playlist](https://youtube.com/cms-nh88-tutorials)

### Get Help
- **Issues**: [GitHub Issues](https://github.com/your-org/cms-nh88/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/cms-nh88/discussions)
- **Email**: <EMAIL>
- **Slack**: #cms-nh88-support

### Emergency Contact
- **Critical Issues**: <EMAIL>
- **Phone**: +84-xxx-xxx-xxxx (Business hours only)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Laravel Community** for the amazing framework
- **Chart.js Team** for visualization tools
- **Redis Team** for caching solution
- **All Contributors** who made this project possible

---

**Developed with ❤️ by NH88 Development Team**

*Last updated: December 2024*
